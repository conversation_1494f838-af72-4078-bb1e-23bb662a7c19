{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["cDsNg09LC02+wPvZ8aRNdPpfvU8R85QSR0cPRCn1Uh8=", "aDJNY0JL+W0QHCQag4rxqcg3xgGz4QeuJDjsD/CmbWI=", "zt15rKlWkAn+GzzlTPUsZpQ1GKl1nT3HzYRXe+4K0Jw=", "rdJbV5CyuT4lneB2YUsxqhbX7+HMueCmMJCvgXkE29U=", "RWZveVSKt0hm5motHVHKwG9Wclp0VvZ0tTNy+wMTcRk=", "hSePtbdKHglXGv1AdA8P0QLXNtpWR4LXMLr77j+WoS4=", "2BChenqns/0Lu4SMHfq91f5TWgg8RsVqaFyNlBodQsY=", "RlPdZoXiTrvQLIYoBPBCxn1zew9BM3coLW8hLqUV9Q0=", "pZPrfFFpOy4HD5KJdUSu2ydRMrcX/y2H9l9WGsbn/S0=", "Th3e1EczDiiNV03vp4kteuzTg7YfIsZXI8h/A1GVrsU=", "4MU62fS6r71msa14dkVZrlMw1WriM/5DbYVn7Bgx158=", "K6myseMkk9H1kU7kIo+lvmKsoJG7uAYVJNxXPdR6ssc=", "0MXmKD2NUgapT4z5dJ0pM7klu1+lYgVidEJcc1PAATI=", "DEX17WxuLm+NnZUv/LX3ui3IjL1SKItHIV4DwN/V+Qg="], "CachedAssets": {"DEX17WxuLm+NnZUv/LX3ui3IjL1SKItHIV4DwN/V+Qg=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint=xegrsle1tr}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap8fprquuc", "Integrity": "zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "FileLength": 7050, "LastWriteTime": "2025-06-16T16:07:13.5329042+00:00"}, "aDJNY0JL+W0QHCQag4rxqcg3xgGz4QeuJDjsD/CmbWI=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "joqzyw7ssu", "Integrity": "LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 23897, "LastWriteTime": "2025-06-05T19:02:10.4301233+00:00"}, "zt15rKlWkAn+GzzlTPUsZpQ1GKl1nT3HzYRXe+4K0Jw=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbl5ivwo6k", "Integrity": "Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 74905, "LastWriteTime": "2025-06-05T19:02:10.4409008+00:00"}, "rdJbV5CyuT4lneB2YUsxqhbX7+HMueCmMJCvgXkE29U=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "css/components/non-contact-days#[.{fingerprint=fynul0i6py}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fciai1e7r", "Integrity": "o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "FileLength": 246, "LastWriteTime": "2025-06-05T19:02:10.4301233+00:00"}, "0MXmKD2NUgapT4z5dJ0pM7klu1+lYgVidEJcc1PAATI=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint=xegrsle1tr}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap8fprquuc", "Integrity": "zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "FileLength": 7050, "LastWriteTime": "2025-06-16T16:07:13.5329042+00:00"}, "hSePtbdKHglXGv1AdA8P0QLXNtpWR4LXMLr77j+WoS4=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/buttons#[.{fingerprint=bo27jtqb4m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqsll1jzfq", "Integrity": "RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "FileLength": 949, "LastWriteTime": "2025-06-05T19:02:10.4220901+00:00"}, "4MU62fS6r71msa14dkVZrlMw1WriM/5DbYVn7Bgx158=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/modals#[.{fingerprint=78sdthvano}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6wbrcxc5wq", "Integrity": "Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "FileLength": 627, "LastWriteTime": "2025-06-16T16:07:13.5308978+00:00"}, "RlPdZoXiTrvQLIYoBPBCxn1zew9BM3coLW8hLqUV9Q0=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/fonts#[.{fingerprint=en88zmxjpn}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1edd6l2s4", "Integrity": "XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "FileLength": 256, "LastWriteTime": "2025-06-05T19:02:10.4200833+00:00"}, "cDsNg09LC02+wPvZ8aRNdPpfvU8R85QSR0cPRCn1Uh8=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "app#[.{fingerprint=67x8kjth1g}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqvvn45c3v", "Integrity": "l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "FileLength": 1304, "LastWriteTime": "2025-06-16T16:07:13.5329042+00:00"}, "pZPrfFFpOy4HD5KJdUSu2ydRMrcX/y2H9l9WGsbn/S0=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/forms#[.{fingerprint=3rfrqp8vgl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r03ioh3bfh", "Integrity": "NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "FileLength": 1071, "LastWriteTime": "2025-06-16T16:07:13.5308978+00:00"}, "2BChenqns/0Lu4SMHfq91f5TWgg8RsVqaFyNlBodQsY=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/colors#[.{fingerprint=si6stszvb5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcq0vr1lul", "Integrity": "pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "FileLength": 592, "LastWriteTime": "2025-06-16T16:07:13.5329042+00:00"}, "K6myseMkk9H1kU7kIo+lvmKsoJG7uAYVJNxXPdR6ssc=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/typography#[.{fingerprint=gj0vo58ahm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7bsxntehs", "Integrity": "wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "FileLength": 612, "LastWriteTime": "2025-06-05T19:02:10.4261077+00:00"}, "Th3e1EczDiiNV03vp4kteuzTg7YfIsZXI8h/A1GVrsU=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/layout#[.{fingerprint=hni17gahw5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hnnp3rbu3x", "Integrity": "/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "FileLength": 1492, "LastWriteTime": "2025-06-16T16:07:13.5349118+00:00"}, "RWZveVSKt0hm5motHVHKwG9Wclp0VvZ0tTNy+wMTcRk=": {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "css/lap-assessment-summary#[.{fingerprint=n8wh2mfms8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rco1797kwl", "Integrity": "dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "FileLength": 1277, "LastWriteTime": "2025-06-16T16:07:13.5329042+00:00"}}, "CachedCopyCandidates": {}}