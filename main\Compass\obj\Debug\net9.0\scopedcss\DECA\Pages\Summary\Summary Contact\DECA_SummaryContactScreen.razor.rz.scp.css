.deca-contacts-page-title[b-kcrj1nrml4] {
	text-align: center;
	margin-block: 2rem 4rem;
	color: var(--c4l-primary-purple);
}

.contacts-layout[b-kcrj1nrml4] {
	gap: 1rem;
	align-items: flex-start;
}

.contacts-table-container[b-kcrj1nrml4] {
	width: 100%;
	overflow-x: auto;
}

.contacts-table-wrapper[b-kcrj1nrml4] {
	min-width: 37.5rem;
	border: 0.0625rem solid var(--neutral-300);
	border-radius: var(--button-border-radius);
	background-color: var(--white);
}

.contact-row[b-kcrj1nrml4] {
	border-bottom: 0.0625rem solid var(--neutral-200);
	transition: background-color var(--transition-speed) ease;
}

.contact-row:hover[b-kcrj1nrml4] {
	background-color: var(--neutral-100);
}

.contact-row:last-child[b-kcrj1nrml4] {
	border-bottom: none;
}

.contacts-header-row[b-kcrj1nrml4] {
	background-color: var(--neutral-200);
	border-bottom: 0.125rem solid var(--neutral-300);
}

.table-cell-heading[b-kcrj1nrml4] {
	padding: 0.75rem 0.5rem;
	border-right: 0.0625rem solid var(--neutral-300);
}

.table-cell-heading:last-child[b-kcrj1nrml4] {
	border-right: none;
}

.table-cell[b-kcrj1nrml4] {
	flex: 1;
	padding: 0.5rem;
	border-right: 0.0625rem solid var(--neutral-200);
	min-height: 2.5rem;
}

.table-cell:nth-child(1)[b-kcrj1nrml4],
.table-cell:nth-child(2)[b-kcrj1nrml4],
.table-cell:nth-child(3)[b-kcrj1nrml4] {
	flex: 1.2;
}

.table-cell:nth-child(4)[b-kcrj1nrml4],
.table-cell:nth-child(5)[b-kcrj1nrml4] {
	flex: 1.5;
}

.table-cell:last-child[b-kcrj1nrml4] {
	border-right: none;
}

@media (min-width: 48rem) {
	.contacts-layout[b-kcrj1nrml4] {
		flex-direction: row;
	}

	.contacts-table-container[b-kcrj1nrml4] {
		overflow-x: visible;
	}
}
