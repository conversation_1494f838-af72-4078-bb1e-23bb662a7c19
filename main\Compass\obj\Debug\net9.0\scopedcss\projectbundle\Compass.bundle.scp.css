/* _content/Compass/C4L/Pages/Student/GameCenterComponent.razor.rz.scp.css */
.classroom-selection-form[b-rl49444blp] {
    margin-top: 1.5rem;
    margin-bottom: 2rem;
}

.classroom-option[b-rl49444blp] {
    padding: 0.5rem 0;
}

.classroom-option-label[b-rl49444blp] {
    display: flex;
    position: relative;
    justify-content: space-between;
    width: 100%;
}

/* _content/Compass/Common/Controls/Generic/ContactSummary.razor.rz.scp.css */
.contact-summary-section[b-1x6fzyxawp] {
  gap: 1rem;
}

.summary-wrapper[b-1x6fzyxawp] {
  flex-direction: column;
}

@media (min-width: 40rem) {
  .contact-summary-section[b-1x6fzyxawp] {
    gap: 0.5rem;
  }

  .summary-wrapper[b-1x6fzyxawp] {
    flex-direction: row;
    gap: 0.75rem;
  }
}
/* _content/Compass/Common/Controls/Generic/LoaderComponent.razor.rz.scp.css */
.loader-wrapper[b-9k7n9yygxl] {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner[b-9k7n9yygxl] {
  width: clamp(2.5rem, 1.055rem + 6.163vw, 5rem);
  height: clamp(2.5rem, 1.055rem + 6.163vw, 5rem);
  border-radius: 50%;
  border: clamp(0.25rem, 0.069rem + 0.77vw, 0.563rem) solid;
  border-color: hsl(200 40% 90%);
  border-right-color: var(--c4l-secondary-teal);
  animation: loading-spinner-b-9k7n9yygxl 2.5s infinite linear;
}

@keyframes loading-spinner-b-9k7n9yygxl {
  to {
    transform: rotate(1turn);
  }
}
/* _content/Compass/Common/Controls/Generic/NoTableDataMessage.razor.rz.scp.css */
.no-data-message-wrapper[b-y7duacshej] {
  --_info-text-color: hsl(236.4, 68.6%, 23.7%);
  --_info-bg-color: hsl(190 89.7% 87.5%);
  --_info-success-text-color: hsl(152 69% 19%);
  --_info-success-bg-color: hsl(151, 100%, 95%);
  --_info-warning-text-color: hsl(45 94% 21%);
  --_info-warning-bg-color: hsl(46, 100%, 97%);
  --_info-danger-text-color: hsl(354 79.4% 21%);
  --_info-danger-bg-color: hsl(354, 100%, 97%);
  background-color: var(--_info-bg-color);
  color: var(--_info-text-color);
  border-radius: 0.25rem;
  border-inline-start: 0.375rem solid var(--_info-text-color);
  padding: 1rem;

  & a {
    font-weight: 500;
    color: currentColor;
  }

  &.success-message[b-y7duacshej] {
    background-color: var(--_info-success-bg-color);
    border-color: var(--_info-success-text-color);
    color: var(--_info-success-text-color);

    & a {
      color: currentColor;
    }
  }

  &.warning-message[b-y7duacshej] {
    background-color: var(--_info-warning-bg-color);
    border-color: var(--_info-warning-text-color);
    color: var(--_info-warning-text-color);

    & a {
      color: currentColor;
    }
  }

  &.danger-message[b-y7duacshej] {
    background-color: var(--_info-danger-bg-color);
    border-color: var(--_info-danger-text-color);
    color: var(--_info-danger-text-color);

    & a {
      color: currentColor;
    }
  }
}
/* _content/Compass/Common/Controls/Generic/TooltipText.razor.rz.scp.css */
.c4l-tooltip-content[b-gml6luaeic] {
  cursor: help;
}

.tooltip-wrapper[b-gml6luaeic] {
  font-size: 0.875rem;
  font-weight: 500;
  display: block;
  position: absolute;
  padding: 0.5rem 0.75rem;
  top: 1.75rem;
  left: 0;
  background: var(--c4l-primary-purple);
  color: var(--white);
  border-radius: var(--border-radius, 0.25rem);
  z-index: 100;
  width: max-content;
  max-width: 300px;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: opacity var(--transition-speed) ease, visibility var(--transition-speed) ease;

  &.visible {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
  }
}

.tooltip-arrow[b-gml6luaeic] {
  --_arrow-size: 0.5rem;
  border-color: var(--c4l-primary-purple) transparent;
  border-style: solid;
  border-width: 0px var(--_arrow-size) var(--_arrow-size) var(--_arrow-size);
  height: 0px;
  width: 0px;
  display: block;
  position: absolute;
  left: 3rem;

  &.up-arrow {
    top: calc(var(--_arrow-size) * -1);
  }

  &.down-arrow[b-gml6luaeic] {
    top: auto;
    bottom: calc(var(--_arrow-size) * -1);
    border-width: var(--_arrow-size) var(--_arrow-size) 0px var(--_arrow-size);
  }
}
/* _content/Compass/Common/Pages/Admin/General/UserDetails.razor.rz.scp.css */
.user-info-heading[b-qiq5bhph9q] {
  margin-block: 3rem 1.5rem;
}

.user-summary-section[b-qiq5bhph9q] {
  gap: 1rem;
}

.user-details-wrapper[b-qiq5bhph9q] {
  flex-direction: column;
}

@media (min-width: 40rem) {
  .user-details-wrapper[b-qiq5bhph9q] {
    flex-direction: row;
    gap: 0.75rem;
  }
}
/* _content/Compass/Common/Pages/Admin/Organization/OrganizationManageComponent.razor.rz.scp.css */
.manage-organization-component[b-j9l3it36dk] {
  margin-block-start: 3rem;
}
/* _content/Compass/Common/Pages/Admin/Site/SiteSummaryComponent.razor.rz.scp.css */
.summary-section-wrapper[b-lrhya0gfmr] {
  border-radius: 0.25rem;
  margin-block: 3rem;
  margin-inline: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: fit-content;
  min-width: 600px;
}

.summary-title[b-lrhya0gfmr] {
  background: var(--c4l-primary-purple);
  padding: 1.25rem;
  border-radius: 0.25rem 0.25rem 0 0;
}

.summary-title-underline[b-lrhya0gfmr] {
  display: block;
  width: 50px;
  height: 3px;
  background: var(--c4l-secondary-teal);
  margin-block-start: 0.25rem;
}

.summary-wrapper[b-lrhya0gfmr] {
  grid-template-columns: 1fr;
  gap: 2rem;
  padding: 1.25rem;
}

.summary-detail[b-lrhya0gfmr] {
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  margin-block-end: 0.5rem;
  border-block-end: 1px solid var(--c4l-primary-purple);
}

.summary-item[b-lrhya0gfmr] {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #333;

  & svg {
    transform: translateY(-2px);
  }
}

@media (min-width: 40rem) {
  .summary-wrapper[b-lrhya0gfmr] {
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  }
}
/* _content/Compass/Common/Pages/Prompts/Generic/DialogBox.razor.rz.scp.css */
.dialog-box[b-8eo4sypzxi] {
  width: min(100% - 2rem, 500px);
  padding: 1.25rem;
}

.dialog-box-heading[b-8eo4sypzxi] {
  margin-block-end: 1rem;
}

.dialog-actions[b-8eo4sypzxi] {
  flex-direction: column;
  gap: 1rem;
  
  & .c4l-button {
    width: 100%;
  }
}

@media (min-width: 48rem) {
  .dialog-actions[b-8eo4sypzxi] {
    flex-direction: row;
    justify-content: center;

    & .c4l-button {
      width: fit-content;
    }
  }
}

@media (min-width: 64rem) {
  .dialog-box[b-8eo4sypzxi] {
    transform: translateX(137.5px);
  }
}
/* _content/Compass/Common/Pages/Prompts/Generic/MessageBox.razor.rz.scp.css */
.messagebox-overlay[b-wdlufz7tr3] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 200;
}

.messagebox[b-wdlufz7tr3] {
  background-color: var(--white);
  padding: 1.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: min(100% - 2rem, 500px);
}

.messagebox-actions[b-wdlufz7tr3] {
  margin-block-start: 1.25rem;
}

.messagebox-actions button[b-wdlufz7tr3] {
  &:hover {
    opacity: 0.9;
  }
}

@media (min-width: 64rem) {
  .messagebox[b-wdlufz7tr3] {
    transform: translateX(137.5px);
  }
}
/* _content/Compass/Components/Account/Pages/Manage/Email.razor.rz.scp.css */
.input-group-append[b-bqlop9dag6] {
  &.manage-email-checkbox-wrapper {
    justify-content: center;
    align-items: center;
    border-radius: 0 0.25rem 0.25rem 0 !important;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    padding: 0.25rem;
  }  
}
/* _content/Compass/Components/Account/Pages/Manage/PersonalData.razor.rz.scp.css */
.personal-data-subheading[b-0zmrm5puny] {
  width: min(100%, 650px);
  margin-inline: auto;
  margin-block: 2rem;
}

.manage-data-form-wrapper[b-0zmrm5puny] {
  gap: 1rem;
  justify-content: center;
}

.personal-data-buttons-wrapper[b-0zmrm5puny] {
  margin-block: 0;

  & button {
    width: 100%;
    text-align: center;
  }
}

@media (min-width: 48rem) {
  .personal-data-buttons-wrapper[b-0zmrm5puny] {
    flex-direction: row;

    & button {
      width: fit-content;
    }
  }
}
/* _content/Compass/Components/Account/Pages/UserLogin.razor.rz.scp.css */
.additional-login-links[b-ybmlykrdov] {
  margin-block-start: 1.5rem;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 64rem) {
  .additional-login-links[b-ybmlykrdov] {
    flex-direction: row;
    justify-content: space-between;

    & .c4l-button {
      width: 100%;
    }
  }
}
/* _content/Compass/Components/Account/Shared/ExternalLoginPicker.razor.rz.scp.css */
.external-logins-wrapper[b-tcrykwbyy5] {
  flex-direction: column;
  gap: 1rem;
}

.external-login-button[b-tcrykwbyy5] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  height: 42px;

  & svg {
    margin-block-end: 0.2rem;
  }
}

@media (min-width: 64rem) {
  .external-logins-wrapper[b-tcrykwbyy5] {
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}
/* _content/Compass/Components/Account/Shared/ManageLayout.razor.rz.scp.css */
.manage-account-grid-wrapper[b-r7oaa0maw0] {
  grid-template-columns: 1fr;
  margin-block-start: 6rem;
}

.manage-account-content[b-r7oaa0maw0] {
  width: min(100%, 800px);
  margin-inline: auto;
}
/* _content/Compass/Components/Layout/FooterComponent.razor.rz.scp.css */
.footer[b-gm6zg3kqqd] {
  background-color: var(--c4l-light-gray);
  padding-block: 1.75rem;
  --_footer-facade-height: 1.5rem;

  &::before {
    content: '';
    position: absolute;
    top: calc(var(--_footer-facade-height) * -1);
    left: 0;
    width: 100%;
    height: var(--_footer-facade-height);
    background: linear-gradient(0deg, var(--white) 0, hsla(0, 0%, 100%, 0.3333) 100%);
    box-shadow: rgba(255, 255, 255, 0.16) 0px -8px 6px, rgba(255, 255, 255, 0.23) 0px -4px 6px;
  }
}

.footer-content-wrapper[b-gm6zg3kqqd] {
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.footer-links-wrapper[b-gm6zg3kqqd] {
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

[b-gm6zg3kqqd] .footer-link,
.footer-copyright-text[b-gm6zg3kqqd] {
  font-size: 1.125rem;
  color: var(--c4l-dark-gray);
  margin-block: 0;
}

[b-gm6zg3kqqd] .footer-link {
  text-decoration: none;
}

.footer-social-links-wrapper[b-gm6zg3kqqd] {
  gap: 0.75rem;
  align-items: center;
  margin-block-start: 0.5rem;
}

@media (min-width: 64rem) {
  .footer[b-gm6zg3kqqd] {
    &::before {
      left: 285px;
      width: calc(100% - 285px);
    }
  }
}

@media (min-width: 80rem) {
  .footer-content-wrapper[b-gm6zg3kqqd] {
    flex-direction: row;
    justify-content: space-between;
    gap: 0;
  }

  .footer-links-wrapper[b-gm6zg3kqqd] {
    flex-direction: row;
    justify-content: flex-start;
  }

  [b-gm6zg3kqqd] .footer-link {
    transition: color var(--transition-speed) ease;

    &:hover {
      color: var(--c4l-primary-purple);
    }
  }

  .footer-social-path[b-gm6zg3kqqd] {
    transition: fill var(--transition-speed) ease;
  }

  .footer-social-links-wrapper[b-gm6zg3kqqd] {
    margin-block-start: 0rem;
  }

  .footer-social-link[b-gm6zg3kqqd] {
    &:hover {
      & .footer-social-path {
        fill: var(--c4l-primary-purple);
      }
    }
  }
}
/* _content/Compass/Components/Layout/HeaderComponent.razor.rz.scp.css */
.header[b-rkgraz988p] {
  padding-block: 1rem;
  padding-inline: clamp(1.5rem, 1.211rem + 1.233vw, 2rem);
}

.header-content-wrapper[b-rkgraz988p] {
  width: min(100%, 1600px);
  margin-inline: auto;
  align-items: center;
  justify-content: space-between;
}

.header-buttons-wrapper[b-rkgraz988p] {
  gap: 0.75rem;
  align-items: center;
}

.header-user-links-wrapper[b-rkgraz988p] {
  display: none;
}

.language-switcher-button[b-rkgraz988p] {
  &:hover {
    background-color: hsl(278 37% 90%);
    color: var(--c4l-primary-purple);
  }
}

.header-user-info-wrapper[b-rkgraz988p] {
  gap: 0.75rem;
  align-items: center;
  color: var(--c4l-primary-purple);
  display: none;
}

.header-user-avatar[b-rkgraz988p] {
  width: 2rem;
  aspect-ratio: 1;
  border-radius: 50%;
  background-color: var(--c4l-secondary-teal);
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (min-width: 64rem) {
  .header[b-rkgraz988p] {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: var(--white);
    padding-inline: 2rem;
    padding-block: 1.3125rem;
    height: 5.3125rem;
    border-block-end: 1px solid var(--Colors-Border-Base-standard, #B3BBC2);

    &.no-login {
      background-color: var(--c4l-primary-purple);
    }
  }

  .header-user-info-wrapper[b-rkgraz988p] {
    display: flex;
  }

  .header-user-avatar[b-rkgraz988p] {
    width: 2.75rem;
  }
}
/* _content/Compass/Components/Layout/MainLayout.razor.rz.scp.css */
.page[b-z0jkwapr3j] {
    position: relative;
    grid-template-columns: auto;
    grid-template-rows: auto;
    height: 100vh;
    align-content: space-between;

    &[data-logged-in] {
        grid-template-columns: 1fr;
        grid-template-rows: 56px 1fr;
        align-content: normal;
        height: auto;
    }
}

.main-wrapper[b-z0jkwapr3j] {
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    overflow: auto;
}

.page-content-wrapper[b-z0jkwapr3j] {
    flex-direction: column;
}

.top-row[b-z0jkwapr3j] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: space-between;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

.top-row[b-z0jkwapr3j]  a, .top-row[b-z0jkwapr3j]  .btn-link {
    white-space: nowrap;
    margin-left: 0;
    text-decoration: none;
}

.top-row[b-z0jkwapr3j]  a:hover, .top-row[b-z0jkwapr3j]  .btn-link:hover {
    text-decoration: underline;
}

.top-row[b-z0jkwapr3j]  a:first-child {
    overflow: hidden;
    text-overflow: ellipsis;
}

#blazor-error-ui[b-z0jkwapr3j] {
    font-weight: 500;
    display: none;
    background: hsl(42 100% 92.5%);
    color: hsl(33 100% 20%);
    position: fixed;
    bottom: 0;
    left: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    width: 100%;
    z-index: 1000;
    text-align: center;
}

#blazor-error-ui .dismiss[b-z0jkwapr3j] {
    cursor: pointer;
    margin-inline-start: 0.125rem;
}

@media (min-width: 64rem) {
    .page[b-z0jkwapr3j] {
        --_sidebar-desktop-width: 285px;
        
        &[data-logged-in] {
            grid-template-columns: var(--_sidebar-desktop-width) 1fr;
            grid-template-rows: auto;

            & .main-wrapper {
                height: 100vh;
            }
        }
    }

    .top-row[b-z0jkwapr3j] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-z0jkwapr3j]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-z0jkwapr3j]  a,
    .top-row[b-z0jkwapr3j]  .btn-link {
        margin-left: 1.5rem;
    }

    .top-row[b-z0jkwapr3j], article[b-z0jkwapr3j] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* _content/Compass/Components/Layout/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-dltbumwrg5] {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
}

.navbar-toggler[b-dltbumwrg5] {
    &:checked {
        background-color: rgba(255, 255, 255, 0.5);

        & ~ .nav-scrollable {
            display: block;
            background-color: var(--neutral-900);
            border-block-start: 1px solid var(--neutral-500);
        }
    }
}

.sidebar-nav[b-dltbumwrg5] {
    --_nav-link-text-icon-gap: 0.75rem;
    background-color: var(--neutral-900);
}

.sidebar-dropdown-wrapper[b-dltbumwrg5] {
    height: 3.5rem;
    background-color: var(--neutral-900);
    padding-inline: clamp(1.5rem, 1.211rem + 1.233vw, 2rem);
}

[b-dltbumwrg5] .nav-app-link {
    font-size: 1.25rem;
    color: var(--white);
    text-decoration: none;
}

.bi[b-dltbumwrg5] {
    display: flex;
    width: 1.25rem;
    height: 1.25rem;
    flex: 0 0 1.25rem;
}

.nav-links-wrapper[b-dltbumwrg5] {
    display: flex;
    gap: 0.25rem;
}

.nav-item[b-dltbumwrg5]  .nav-link {
    font-size: 1rem;
    font-weight: 400;
    display: flex;
    gap: var(--_nav-link-text-icon-gap);
    align-items: center;
    background: none;
    border: none;
    border-radius: 0.25rem;
    width: 100%;
    height: 3rem;
    line-height: 1.375;
    padding-inline: 0.5rem;
    transition:
        color var(--transition-speed) ease,
        background-color var(--transition-speed) ease,
        border-color var(--transition-speed) ease;

    &.active {
        font-weight: 600;
        background-color: var(--c4l-primary-500);
        color: var(--white);
    }
}

.nav-item[b-dltbumwrg5]  .nav-link:active,
.nav-item[b-dltbumwrg5]  .nav-link:hover,
.nav-item[b-dltbumwrg5]  .nav-link:focus {
    background-color: var(--c4l-primary-400);
    color: var(--white);
}

.nav-scrollable[b-dltbumwrg5] {
    display: none;
    z-index: 99;
    padding: 1rem;
}

.account-sidebar-nav-link[b-dltbumwrg5],
.login-sidebar-nav-link[b-dltbumwrg5],
.logout-sidebar-nav-link[b-dltbumwrg5] {
    display: block;
}

.account-links-wrapper[b-dltbumwrg5] {
    display: none;
}

.account-settings-link[b-dltbumwrg5],
.account-logout-link[b-dltbumwrg5] {
    background-color: transparent;
    gap: var(--_nav-link-text-icon-gap);
    border-radius: 0.25rem;
    border: none;
    width: 100%;

    &:active,
    &.active,
    &:hover,
    &:focus {
        background-color: var(--c4l-primary-400);
        color: var(--white);
    }
}

@media (min-width: 64rem) {
    .sidebar-nav[b-dltbumwrg5] {
        height: 100vh;
        overflow-y: auto;
    }

    .sidebar-dropdown-wrapper[b-dltbumwrg5] {
        padding: 1rem;
        height: 5.375rem;
        border-block-end: 1px solid var(--neutral-500);

        & .dropdown,
        & .dropdown-toggle,
        & .dropdown-menu {
            width: 100%;
        }

        & .dropdown[b-dltbumwrg5],
        & .dropdown-toggle[b-dltbumwrg5] {
            height: 100%;
        }

        & .compass-app-link[b-dltbumwrg5] {
            color: var(--white);
            background-color: var(--neutral-900);
            border: none;

            &::after {
                content: none;
            }
        }

        & .dropdown-menu[b-dltbumwrg5] {
            padding: 0.5rem;
            border: none;
            top: 56px;
        }

        & .dropdown-menu-li[b-dltbumwrg5] {
            padding: 0.5rem 1rem;
            background-color: var(--white);
            transition: background-color var(--transition-speed) ease;
            border-radius: 0.25rem;

            &:active,
            &:hover,
            &:focus {
                background-color: hsl(206 30% 95%);
            }
        }

        &[b-dltbumwrg5]  .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: hsl(217 17% 15%);
            padding: 0;
            border-radius: 0.25rem;

            &.active {
                background-color: transparent;
            }
        }
    }

    .navbar-toggler[b-dltbumwrg5] {
        display: none;

        &:checked {
            & ~ .nav-scrollable {
                border: none;
            }
        }
    }

    .nav-scrollable[b-dltbumwrg5] {
        display: block;
    }

    .nav-item[b-dltbumwrg5]  .nav-link {
        & svg g,
        & svg path {
            transition: fill var(--transition-speed) ease;
        }
    }
    
    .nav-item[b-dltbumwrg5]  .nav-link:hover {
        & svg g,
        & svg path {
            fill: var(--black);
        }
    }

    .account-sidebar-nav-link[b-dltbumwrg5],
    .login-sidebar-nav-link[b-dltbumwrg5],
    .logout-sidebar-nav-link[b-dltbumwrg5] {
        display: none;
    }

    .account-links-wrapper[b-dltbumwrg5] {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        border-block-start: 1px solid var(--neutral-500);
        padding: 1rem;
    }
}
/* _content/Compass/Components/Pages/Accessibility/Accessibility.razor.rz.scp.css */
.accessibility-section[b-nojt9lf5hj] {
  max-width: 1000px;
  margin-block-start: 6rem;

  & p {
    font-size: 1.125rem;
  }
}

.accessibility-contact-list[b-nojt9lf5hj] {
  font-size: 1.125rem;
  list-style: none;
  padding-inline-start: 1rem;
}

.accessibility-contact-li[b-nojt9lf5hj] {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -1rem;
    transform: translateY(-50%);
    background-color: var(--c4l-primary-purple);
    width: 0.25rem;
    height: 0.25rem;
    border-radius: 50%;
  }
}
/* _content/Compass/DECA/Pages/DECA_StudentGroupRatingSummary.razor.rz.scp.css */
.deca-rating-summary-title[b-brc1gvenz7] {
    text-align: center;
    margin-block: 2rem 4rem;
    color: var(--c4l-primary-purple);
}

.roster-header[b-brc1gvenz7] {
    margin-bottom: 1.25rem;
}

.class-roster-title[b-brc1gvenz7] {
    color: var(--c4l-primary-purple);
    margin: 0;
    font-size: 1.5rem;
}

.search-input[b-brc1gvenz7] {
    padding: 0.5rem 0.75rem;
    border: 0.0625rem solid var(--neutral-300);
    border-radius: 0.25rem;
    width: 18.75rem;
    font-size: 1rem;
    transition:
      border-color var(--transition-speed) ease,
      opacity var(--transition-speed) ease;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        box-shadow: 0 0 0 0.125rem rgba(120, 53, 120, 0.25);
        opacity: 0;
        border-radius: 0.25rem;
        transition: opacity var(--transition-speed) ease;
    }

    &:hover[b-brc1gvenz7] {
        &::before {
            opacity: 1;
        }
    }

    &:focus[b-brc1gvenz7] {
        outline: none;
        border-color: var(--c4l-primary-purple);

        &::before {
            opacity: 1;
        }
    }
}

.class-roster[b-brc1gvenz7] {
    padding: 1.25rem;
}

.roster-row[b-brc1gvenz7] {
    border: 0.0625rem solid var(--neutral-200);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    background-color: var(--white);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.student-info-bar[b-brc1gvenz7] {
    padding: 0.75rem;
    cursor: pointer;
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    border-radius: 0.25rem 0.25rem 0 0;
    transition:
      background-color var(--transition-speed) ease,
      color var(--transition-speed) ease;
}

.student-info-bar:hover[b-brc1gvenz7] {
    background-color: var(--neutral-500);
    color: var(--neutral-200);
}

.student-info-bar:hover .student-name[b-brc1gvenz7] {
    color: var(--neutral-200);
}

.student-info-bar:hover .view-child-btn[b-brc1gvenz7] {
    color: var(--neutral-200);
}

.student-info-bar:focus[b-brc1gvenz7] {
    outline: 0.125rem solid var(--white);
    outline-offset: -0.125rem;
}

.student-name[b-brc1gvenz7] {
    color: var(--neutral-500);
    margin: 0;
    transition: color var(--transition-speed) ease;
}

.view-child-btn[b-brc1gvenz7] {
    background-color: transparent;
    color: var(--neutral-500);
    border: 0.0625rem solid var(--neutral-300);
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition:
      background-color var(--transition-speed) ease,
      color var(--transition-speed) ease;
    gap: 0.5rem;
}

.student-info-bar .view-child-btn:hover[b-brc1gvenz7] {
    background-color: var(--neutral-100);
    color: var(--neutral-500);
}

.view-child-btn:focus[b-brc1gvenz7] {
    outline: 0.125rem solid var(--neutral-400);
    outline-offset: 0.125rem;
}

.rating-details[b-brc1gvenz7] {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-speed) ease-out;
}

.rating-details.expanded[b-brc1gvenz7] {
    max-height: 62.5rem;
    padding: 1rem;
    overflow-x: auto;
    overflow-y: hidden;
}

.ratings-grid-top[b-brc1gvenz7] {
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    margin-top: 0.625rem;
    width: max-content;
    border: 0.0625rem solid var(--neutral-300);
    border-bottom: none;
}

.grid-cell[b-brc1gvenz7] {
    flex: 0 0 6.875rem;
    width: 6.875rem;
    padding: 0.375rem;
    text-align: center;
    border: 0.0625rem solid rgba(255, 255, 255, 0.2);
    font-size: 0.75rem;
    min-height: 3rem;
    color: var(--neutral-500);
    text-transform: uppercase;
    white-space: normal;
    overflow: visible;
    word-wrap: break-word;
}

.grid-group[b-brc1gvenz7] {
    flex: 0 0 14.0625rem;
    width: 14.0625rem;
    border-right: 0.0625rem solid rgba(255, 255, 255, 0.2);
}

.grid-group-title[b-brc1gvenz7] {
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    margin: 0;
    border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.2);
    background-color: var(--neutral-200);
    color: var(--neutral-500);
    text-transform: uppercase;
}

.grid-subgroup[b-brc1gvenz7] {
    flex: 1;
}

.grid-subcell[b-brc1gvenz7] {
    flex: 0 0 2.8125rem;
    width: 2.8125rem;
    padding: 0.25rem;
    text-align: center;
    border: 0.0625rem solid rgba(255, 255, 255, 0.2);
    font-size: 0.625rem;
    min-height: 2rem;
    color: var(--neutral-500);
    text-transform: uppercase;
    white-space: normal;
    overflow: visible;
    word-wrap: break-word;
}

.ratings-grid-content[b-brc1gvenz7] {
    width: max-content;
    border: 0.0625rem solid var(--neutral-300);
    border-top: none;
}

.rating-data-row[b-brc1gvenz7] {
    border-bottom: 0.0625rem solid var(--neutral-200);
    transition: background-color var(--transition-speed) ease;
}

.rating-data-row:hover[b-brc1gvenz7] {
    background-color: var(--neutral-100);
}

.rating-data-row:last-child[b-brc1gvenz7] {
    border-bottom: none;
}

.data-cell[b-brc1gvenz7] {
    flex: 0 0 6.875rem;
    width: 6.875rem;
    padding: 0.375rem;
    text-align: center;
    border-right: 0.0625rem solid var(--neutral-200);
    font-size: 0.75rem;
    min-height: 2.5rem;
    word-wrap: break-word;
    background-color: var(--white);
}

.data-group[b-brc1gvenz7] {
    flex: 0 0 14.0625rem;
    width: 14.0625rem;
    border-right: 0.0625rem solid var(--neutral-200);
}

.data-subcell[b-brc1gvenz7] {
    flex: 0 0 2.8125rem;
    width: 2.8125rem;
    padding: 0.25rem;
    text-align: center;
    border-right: 0.0625rem solid var(--neutral-200);
    font-size: 0.625rem;
    min-height: 2.5rem;
    word-wrap: break-word;
    background-color: var(--white);
}

.rating-actions[b-brc1gvenz7] {
    gap: 0.5rem;
}

@media (min-width: 48rem) {
    .deca-rating-summary-title[b-brc1gvenz7] {
        margin-block: 1rem 4rem;
    }

    .roster-header[b-brc1gvenz7] {
        gap: 1rem;
    }

    .search-input[b-brc1gvenz7] {
        width: 100%;
    }

    .grid-cell[b-brc1gvenz7],
    .data-cell[b-brc1gvenz7],
    .grid-subcell[b-brc1gvenz7],
    .data-subcell[b-brc1gvenz7] {
        padding: 0.25rem;
        font-size: 0.625rem;
        min-height: 2rem;
    }

    .grid-group-title[b-brc1gvenz7] {
        font-size: 0.625rem;
        padding: 0.25rem;
    }

    .student-info-bar[b-brc1gvenz7] {
        gap: 0.5rem;
    }
}

@media (min-width: 75rem) {
    .grid-cell[b-brc1gvenz7],
    .data-cell[b-brc1gvenz7],
    .grid-subcell[b-brc1gvenz7],
    .data-subcell[b-brc1gvenz7] {
        padding: 0.375rem;
        font-size: 0.75rem;
    }

    .grid-group-title[b-brc1gvenz7] {
        font-size: 0.75rem;
        padding: 0.375rem;
    }
}
/* _content/Compass/DECA/Pages/Summary/Summary Contact/DECA_SummaryContactScreen.razor.rz.scp.css */
.deca-contacts-page-title[b-kcrj1nrml4] {
	text-align: center;
	margin-block: 2rem 4rem;
	color: var(--c4l-primary-purple);
}

.contacts-layout[b-kcrj1nrml4] {
	gap: 1rem;
	align-items: flex-start;
}

.contacts-table-container[b-kcrj1nrml4] {
	width: 100%;
	overflow-x: auto;
}

.contacts-table-wrapper[b-kcrj1nrml4] {
	min-width: 37.5rem;
	border: 0.0625rem solid var(--neutral-300);
	border-radius: var(--button-border-radius);
	background-color: var(--white);
}

.contact-row[b-kcrj1nrml4] {
	border-bottom: 0.0625rem solid var(--neutral-200);
	transition: background-color var(--transition-speed) ease;
}

.contact-row:hover[b-kcrj1nrml4] {
	background-color: var(--neutral-100);
}

.contact-row:last-child[b-kcrj1nrml4] {
	border-bottom: none;
}

.contacts-header-row[b-kcrj1nrml4] {
	background-color: var(--neutral-200);
	border-bottom: 0.125rem solid var(--neutral-300);
}

.table-cell-heading[b-kcrj1nrml4] {
	padding: 0.75rem 0.5rem;
	border-right: 0.0625rem solid var(--neutral-300);
}

.table-cell-heading:last-child[b-kcrj1nrml4] {
	border-right: none;
}

.table-cell[b-kcrj1nrml4] {
	flex: 1;
	padding: 0.5rem;
	border-right: 0.0625rem solid var(--neutral-200);
	min-height: 2.5rem;
}

.table-cell:nth-child(1)[b-kcrj1nrml4],
.table-cell:nth-child(2)[b-kcrj1nrml4],
.table-cell:nth-child(3)[b-kcrj1nrml4] {
	flex: 1.2;
}

.table-cell:nth-child(4)[b-kcrj1nrml4],
.table-cell:nth-child(5)[b-kcrj1nrml4] {
	flex: 1.5;
}

.table-cell:last-child[b-kcrj1nrml4] {
	border-right: none;
}

@media (min-width: 48rem) {
	.contacts-layout[b-kcrj1nrml4] {
		flex-direction: row;
	}

	.contacts-table-container[b-kcrj1nrml4] {
		overflow-x: visible;
	}
}
/* _content/Compass/LAP/Pages/Student/Assessment/Summary/LAP_StudentAssessmentSummary.razor.rz.scp.css */
.assessment-summary[b-ein11z8iew] {
    --border-radius: var(--button-border-radius);
    --cell-size: 3rem;
    --checkpoint-width: 8rem;
    --section-gap: 1.5rem;

    --elap-column-count: 6;
    --lapd-column-count: 13;
    --elap-internal-gaps: 0.3125rem;
    --lapd-internal-gaps: 0.75rem;

    --header-light-purple: hsl(278 37.3% 87.5%);
    --date-color: var(--c4l-tertiary-yellow);

    --completed-color: hsl(152 69% 30%);
    --started-color: hsl(190 80% 20%);
    --invalid-color: var(--c4l-danger);
    --low-scores-color: hsl(32 100% 30%);
    --no-item-color: hsl(0 0% 45%);

    --completed-border: hsl(152 69% 20%);
    --started-border: hsl(190 80% 10%);
    --invalid-border: var(--c4l-alert-danger);
    --low-scores-border: hsl(32 100% 20%);
    --no-item-border: hsl(0 0% 35%);
}

.assessment-summary-title[b-ein11z8iew] {
    margin-block-start: -0.5rem;
}

.assessment-summary-wrapper[b-ein11z8iew] {
    margin-block: 6rem 2rem;
    width: min(100%, 1600px);
    margin-inline: auto;
}

.info-legend-container[b-ein11z8iew] {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-block-end: 0.625rem;
}

.student-info[b-ein11z8iew] {
    padding: 1rem;
    border-radius: var(--border-radius);
}

.student-name[b-ein11z8iew], .student-dob[b-ein11z8iew] {
    font-size: 1rem;
    font-weight: 600;
    color: var(--c4l-primary-purple);
    margin-block-end: 0.3125rem;
}

.student-dob[b-ein11z8iew] {
    margin-block-end: 0;
}

.progress-legend[b-ein11z8iew] {
    display: flex;
    gap: 1.25rem;
    align-items: center;
    padding: 0.625rem;
    border-radius: var(--border-radius);
}

.legend-item[b-ein11z8iew] {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-box[b-ein11z8iew] {
    width: 1rem;
    height: 1rem;
    border-radius: 0.125rem;
}

.legend-box.completed[b-ein11z8iew] {
    background-color: var(--completed-color);
}

.legend-box.started[b-ein11z8iew] {
    background-color: var(--started-color);
}

.legend-box.invalid[b-ein11z8iew] {
    background-color: var(--invalid-color);
}

.legend-box.low-scores[b-ein11z8iew] {
    background-color: var(--low-scores-color);
}

.legend-box.no-item[b-ein11z8iew] {
    background-color: var(--no-item-color);
}

.assessment-table[b-ein11z8iew] {
    font-size: 0.875rem;
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid var(--c4l-primary-purple);
    border-radius: var(--border-radius);
    width: fit-content;
    margin: 0 auto;
    position: relative;
    padding: 0.5rem;
    overflow: visible;
}

.assessment-table[b-ein11z8iew]::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: transparent;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
    pointer-events: none;
}

.assessment-table:hover[b-ein11z8iew]::before {
    opacity: 1;
}

.table-header[b-ein11z8iew] {
    display: flex;
    align-items: stretch;
    gap: var(--section-gap);
    background-color: var(--header-light-purple);
    margin: -0.5rem -0.5rem 0 -0.5rem;
    padding: 0.5rem 0.5rem 0 0.5rem;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.checkpoint-column[b-ein11z8iew] {
    width: var(--checkpoint-width);
    display: flex;
    flex-direction: column;
}

.elap-section[b-ein11z8iew], .lapd-section[b-ein11z8iew] {
    display: flex;
    flex-direction: column;
}

.elap-section[b-ein11z8iew] {
    width: calc(var(--elap-column-count) * var(--cell-size) + var(--elap-internal-gaps));
    flex-shrink: 0;
}

.lapd-section[b-ein11z8iew] {
    width: calc(var(--lapd-column-count) * var(--cell-size) + var(--lapd-internal-gaps));
    flex-shrink: 0;
}

.header-cell[b-ein11z8iew], .section-header[b-ein11z8iew] {
    background-color: var(--header-light-purple);
    color: var(--c4l-primary-purple);
    font-weight: 600;
    text-align: center;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkpoint-header[b-ein11z8iew] {
    flex: 1;
}

.subheader-row[b-ein11z8iew] {
    display: flex;
    gap: 0.0625rem;
}

.subheader-cell[b-ein11z8iew] {
    background-color: var(--header-light-purple);
    color: var(--c4l-primary-purple);
    font-weight: 600;
    text-align: center;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: var(--cell-size);
}

.assessment-row[b-ein11z8iew] {
    display: flex;
    flex-direction: column;
    gap: 0.0625rem;
}

.date-row[b-ein11z8iew], .data-row[b-ein11z8iew] {
    display: flex;
    gap: var(--section-gap);
    background-color: var(--white);
}

.date-cell[b-ein11z8iew] {
    font-size: 0.7rem;
    color: var(--date-color);
    font-weight: 600;
    padding: 0.125rem 0.25rem;
    background-color: var(--white);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem;
}

.checkpoint-cell[b-ein11z8iew] {
    background-color: var(--white);
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2rem;
}

.score-row[b-ein11z8iew] {
    display: flex;
    gap: 0.0625rem;
}

.score-button[b-ein11z8iew] {
    background-color: var(--white);
    border: 0.0625rem solid transparent;
    border-radius: 0.1875rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    min-height: 2rem;
    padding: 0.25rem;
    cursor: pointer;
    transition:
        box-shadow var(--transition-speed) ease,
        background-color var(--transition-speed) ease;
    box-shadow:
        0 0.125rem 0.25rem rgba(0, 0, 0, 0.15),
        0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1),
        inset 0 0.0625rem 0 rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: var(--cell-size);
}

.score-button:focus[b-ein11z8iew] {
    outline: 0.1875rem solid var(--c4l-primary-purple);
    outline-offset: 0.125rem;
    box-shadow:
        0 0.25rem 0.375rem rgba(0, 0, 0, 0.1),
        0 0.125rem 0.25rem rgba(0, 0, 0, 0.06),
        inset 0 0.0625rem 0 rgba(255, 255, 255, 0.4),
        0 0 0 0.25rem rgba(98, 187, 70, 0.25);
    z-index: 1;
    position: relative;
}

.checkpoint-cell:focus[b-ein11z8iew] {
    outline: 0.1875rem solid var(--c4l-primary-purple);
    outline-offset: 0.125rem;
    background-color: var(--header-light-purple);
}

.score-button.completed[b-ein11z8iew] {
    background: linear-gradient(145deg, var(--completed-color), var(--completed-border));
    border-color: var(--completed-border);
}

.score-button.started[b-ein11z8iew] {
    background: linear-gradient(145deg, var(--started-color), var(--started-border));
    border-color: var(--started-border);
}

.score-button.invalid[b-ein11z8iew] {
    background: linear-gradient(145deg, var(--invalid-color), var(--invalid-border));
    border-color: var(--invalid-border);
}

.score-button.low-scores[b-ein11z8iew] {
    background: linear-gradient(145deg, var(--low-scores-color), var(--low-scores-border));
    border-color: var(--low-scores-border);
}

.score-button.no-item[b-ein11z8iew] {
    background: linear-gradient(145deg, var(--no-item-color), var(--no-item-border));
    border-color: var(--no-item-border);
    color: var(--white);
}

@media (prefers-contrast: high) {
    .assessment-summary[b-ein11z8iew] {
        --completed-color: hsl(152 69% 25%);
        --started-color: hsl(190 80% 15%);
        --low-scores-color: hsl(32 100% 25%);
        --no-item-color: hsl(0 0% 40%);
    }

    .score-button:focus[b-ein11z8iew] {
        outline: 0.25rem solid var(--c4l-primary-purple);
        outline-offset: 0.1875rem;
    }
}
