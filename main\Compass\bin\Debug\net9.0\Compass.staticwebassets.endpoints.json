{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Compass.styles.css", "AssetFile": "Compass.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.styles.css", "AssetFile": "Compass.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.styles.css.gz", "AssetFile": "Compass.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}]}, {"Route": "Compass.xegrsle1tr.styles.css", "AssetFile": "Compass.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}, {"Name": "label", "Value": "Compass.styles.css"}]}, {"Route": "Compass.xegrsle1tr.styles.css", "AssetFile": "Compass.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}, {"Name": "label", "Value": "Compass.styles.css"}]}, {"Route": "Compass.xegrsle1tr.styles.css.gz", "AssetFile": "Compass.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}, {"Name": "label", "Value": "Compass.styles.css.gz"}]}, {"Route": "app.67x8kjth1g.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000766283525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "ETag", "Value": "W/\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.67x8kjth1g.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2355"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.67x8kjth1g.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "integrity", "Value": "sha256-l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg="}, {"Name": "label", "Value": "app.css.gz"}]}, {"Route": "app.css", "AssetFile": "app.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000766283525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "ETag", "Value": "W/\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2355"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.css.gz", "AssetFile": "app.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg="}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.gz"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map.gz"}]}, {"Route": "bootstrap/bootstrap.min.css.gz", "AssetFile": "bootstrap/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map.gz", "AssetFile": "bootstrap/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "c4l-favicon.dx84q9xhh9.png", "AssetFile": "c4l-favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2087"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dx84q9xhh9"}, {"Name": "integrity", "Value": "sha256-MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II="}, {"Name": "label", "Value": "c4l-favicon.png"}]}, {"Route": "c4l-favicon.png", "AssetFile": "c4l-favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2087"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II="}]}, {"Route": "css/components/non-contact-days.css", "AssetFile": "css/components/non-contact-days.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004048582996"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "ETag", "Value": "W/\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.css", "AssetFile": "css/components/non-contact-days.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "450"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.css.gz", "AssetFile": "css/components/non-contact-days.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI="}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css", "AssetFile": "css/components/non-contact-days.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004048582996"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "ETag", "Value": "W/\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}, {"Name": "label", "Value": "css/components/non-contact-days.css"}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css", "AssetFile": "css/components/non-contact-days.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "450"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}, {"Name": "label", "Value": "css/components/non-contact-days.css"}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css.gz", "AssetFile": "css/components/non-contact-days.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "integrity", "Value": "sha256-o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI="}, {"Name": "label", "Value": "css/components/non-contact-days.css.gz"}]}, {"Route": "css/lap-assessment-summary.css", "AssetFile": "css/lap-assessment-summary.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000782472613"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "ETag", "Value": "W/\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.css", "AssetFile": "css/lap-assessment-summary.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.css.gz", "AssetFile": "css/lap-assessment-summary.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA="}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css", "AssetFile": "css/lap-assessment-summary.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000782472613"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "ETag", "Value": "W/\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}, {"Name": "label", "Value": "css/lap-assessment-summary.css"}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css", "AssetFile": "css/lap-assessment-summary.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}, {"Name": "label", "Value": "css/lap-assessment-summary.css"}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css.gz", "AssetFile": "css/lap-assessment-summary.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "integrity", "Value": "sha256-dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA="}, {"Name": "label", "Value": "css/lap-assessment-summary.css.gz"}]}, {"Route": "fonts/Avenir Next bold (700).5ksmoc5bde.woff2", "AssetFile": "fonts/Avenir Next bold (700).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "27080"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ksmoc5bde"}, {"Name": "integrity", "Value": "sha256-6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU="}, {"Name": "label", "Value": "fonts/Avenir Next bold (700).woff2"}]}, {"Route": "fonts/Avenir Next bold (700).sbn1h6rvg6.woff", "AssetFile": "fonts/Avenir Next bold (700).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37780"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sbn1h6rvg6"}, {"Name": "integrity", "Value": "sha256-qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q="}, {"Name": "label", "Value": "fonts/Avenir Next bold (700).woff"}]}, {"Route": "fonts/Avenir Next bold (700).woff", "AssetFile": "fonts/Avenir Next bold (700).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37780"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q="}]}, {"Route": "fonts/Avenir Next bold (700).woff2", "AssetFile": "fonts/Avenir Next bold (700).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "27080"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU="}]}, {"Route": "fonts/Avenir Next demi (600).gt4l9a5sa4.woff", "AssetFile": "fonts/Avenir Next demi (600).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37284"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gt4l9a5sa4"}, {"Name": "integrity", "Value": "sha256-pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk="}, {"Name": "label", "Value": "fonts/Avenir Next demi (600).woff"}]}, {"Route": "fonts/Avenir Next demi (600).nlyaynwm5a.woff2", "AssetFile": "fonts/Avenir Next demi (600).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26820"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nlyaynwm5a"}, {"Name": "integrity", "Value": "sha256-q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE="}, {"Name": "label", "Value": "fonts/Avenir Next demi (600).woff2"}]}, {"Route": "fonts/Avenir Next demi (600).woff", "AssetFile": "fonts/Avenir Next demi (600).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37284"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk="}]}, {"Route": "fonts/Avenir Next demi (600).woff2", "AssetFile": "fonts/Avenir Next demi (600).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26820"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE="}]}, {"Route": "fonts/Avenir Next heavy (900).0qzgaa16u2.woff", "AssetFile": "fonts/Avenir Next heavy (900).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37412"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0qzgaa16u2"}, {"Name": "integrity", "Value": "sha256-KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ="}, {"Name": "label", "Value": "fonts/Avenir Next heavy (900).woff"}]}, {"Route": "fonts/Avenir Next heavy (900).44ancgt2kr.woff2", "AssetFile": "fonts/Avenir Next heavy (900).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26884"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "44ancgt2kr"}, {"Name": "integrity", "Value": "sha256-9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s="}, {"Name": "label", "Value": "fonts/Avenir Next heavy (900).woff2"}]}, {"Route": "fonts/Avenir Next heavy (900).woff", "AssetFile": "fonts/Avenir Next heavy (900).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37412"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ="}]}, {"Route": "fonts/Avenir Next heavy (900).woff2", "AssetFile": "fonts/Avenir Next heavy (900).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26884"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s="}]}, {"Route": "fonts/Avenir Next medium (500).bpd8crsurt.woff2", "AssetFile": "fonts/Avenir Next medium (500).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26940"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpd8crsurt"}, {"Name": "integrity", "Value": "sha256-ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk="}, {"Name": "label", "Value": "fonts/Avenir Next medium (500).woff2"}]}, {"Route": "fonts/Avenir Next medium (500).dzm9ctkcvu.woff", "AssetFile": "fonts/Avenir Next medium (500).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37272"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dzm9ctkcvu"}, {"Name": "integrity", "Value": "sha256-Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM="}, {"Name": "label", "Value": "fonts/Avenir Next medium (500).woff"}]}, {"Route": "fonts/Avenir Next medium (500).woff", "AssetFile": "fonts/Avenir Next medium (500).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37272"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM="}]}, {"Route": "fonts/Avenir Next medium (500).woff2", "AssetFile": "fonts/Avenir Next medium (500).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26940"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk="}]}, {"Route": "fonts/Avenir Next regular (400).4v16dk9xw7.woff", "AssetFile": "fonts/Avenir Next regular (400).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "36924"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v16dk9xw7"}, {"Name": "integrity", "Value": "sha256-EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY="}, {"Name": "label", "Value": "fonts/Avenir Next regular (400).woff"}]}, {"Route": "fonts/Avenir Next regular (400).eg2tnwu7tj.woff2", "AssetFile": "fonts/Avenir Next regular (400).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26684"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eg2tnwu7tj"}, {"Name": "integrity", "Value": "sha256-rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ="}, {"Name": "label", "Value": "fonts/Avenir Next regular (400).woff2"}]}, {"Route": "fonts/Avenir Next regular (400).woff", "AssetFile": "fonts/Avenir Next regular (400).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36924"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY="}]}, {"Route": "fonts/Avenir Next regular (400).woff2", "AssetFile": "fonts/Avenir Next regular (400).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26684"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ="}]}, {"Route": "fonts/Avenir Next ultralight (200).1b6w4mpblx.woff2", "AssetFile": "fonts/Avenir Next ultralight (200).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26488"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6w4mpblx"}, {"Name": "integrity", "Value": "sha256-7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc="}, {"Name": "label", "Value": "fonts/Avenir Next ultralight (200).woff2"}]}, {"Route": "fonts/Avenir Next ultralight (200).kn95wx0slf.woff", "AssetFile": "fonts/Avenir Next ultralight (200).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "36732"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kn95wx0slf"}, {"Name": "integrity", "Value": "sha256-IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI="}, {"Name": "label", "Value": "fonts/Avenir Next ultralight (200).woff"}]}, {"Route": "fonts/Avenir Next ultralight (200).woff", "AssetFile": "fonts/Avenir Next ultralight (200).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "36732"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI="}]}, {"Route": "fonts/Avenir Next ultralight (200).woff2", "AssetFile": "fonts/Avenir Next ultralight (200).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26488"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc="}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css", "AssetFile": "stylesheets/buttons.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001052631579"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "ETag", "Value": "W/\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}, {"Name": "label", "Value": "stylesheets/buttons.css"}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css", "AssetFile": "stylesheets/buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4273"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}, {"Name": "label", "Value": "stylesheets/buttons.css"}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css.gz", "AssetFile": "stylesheets/buttons.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "integrity", "Value": "sha256-RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w="}, {"Name": "label", "Value": "stylesheets/buttons.css.gz"}]}, {"Route": "stylesheets/buttons.css", "AssetFile": "stylesheets/buttons.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001052631579"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "ETag", "Value": "W/\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.css", "AssetFile": "stylesheets/buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4273"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.css.gz", "AssetFile": "stylesheets/buttons.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w="}]}, {"Route": "stylesheets/colors.css", "AssetFile": "stylesheets/colors.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001686340641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "ETag", "Value": "W/\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.css", "AssetFile": "stylesheets/colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.css.gz", "AssetFile": "stylesheets/colors.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4="}]}, {"Route": "stylesheets/colors.si6stszvb5.css", "AssetFile": "stylesheets/colors.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001686340641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "ETag", "Value": "W/\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}, {"Name": "label", "Value": "stylesheets/colors.css"}]}, {"Route": "stylesheets/colors.si6stszvb5.css", "AssetFile": "stylesheets/colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}, {"Name": "label", "Value": "stylesheets/colors.css"}]}, {"Route": "stylesheets/colors.si6stszvb5.css.gz", "AssetFile": "stylesheets/colors.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "integrity", "Value": "sha256-pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4="}, {"Name": "label", "Value": "stylesheets/colors.css.gz"}]}, {"Route": "stylesheets/fonts.css", "AssetFile": "stylesheets/fonts.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "ETag", "Value": "W/\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.css", "AssetFile": "stylesheets/fonts.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1534"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.css.gz", "AssetFile": "stylesheets/fonts.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE="}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css", "AssetFile": "stylesheets/fonts.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "ETag", "Value": "W/\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}, {"Name": "label", "Value": "stylesheets/fonts.css"}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css", "AssetFile": "stylesheets/fonts.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1534"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}, {"Name": "label", "Value": "stylesheets/fonts.css"}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css.gz", "AssetFile": "stylesheets/fonts.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "integrity", "Value": "sha256-XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE="}, {"Name": "label", "Value": "stylesheets/fonts.css.gz"}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css", "AssetFile": "stylesheets/forms.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000932835821"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "ETag", "Value": "W/\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}, {"Name": "label", "Value": "stylesheets/forms.css"}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css", "AssetFile": "stylesheets/forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3507"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}, {"Name": "label", "Value": "stylesheets/forms.css"}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css.gz", "AssetFile": "stylesheets/forms.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "integrity", "Value": "sha256-NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc="}, {"Name": "label", "Value": "stylesheets/forms.css.gz"}]}, {"Route": "stylesheets/forms.css", "AssetFile": "stylesheets/forms.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000932835821"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "ETag", "Value": "W/\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.css", "AssetFile": "stylesheets/forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3507"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.css.gz", "AssetFile": "stylesheets/forms.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc="}]}, {"Route": "stylesheets/layout.css", "AssetFile": "stylesheets/layout.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "ETag", "Value": "W/\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.css", "AssetFile": "stylesheets/layout.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6057"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.css.gz", "AssetFile": "stylesheets/layout.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI="}]}, {"Route": "stylesheets/layout.hni17gahw5.css", "AssetFile": "stylesheets/layout.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "ETag", "Value": "W/\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}, {"Name": "label", "Value": "stylesheets/layout.css"}]}, {"Route": "stylesheets/layout.hni17gahw5.css", "AssetFile": "stylesheets/layout.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6057"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}, {"Name": "label", "Value": "stylesheets/layout.css"}]}, {"Route": "stylesheets/layout.hni17gahw5.css.gz", "AssetFile": "stylesheets/layout.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "integrity", "Value": "sha256-/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI="}, {"Name": "label", "Value": "stylesheets/layout.css.gz"}]}, {"Route": "stylesheets/modals.78sdthvano.css", "AssetFile": "stylesheets/modals.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001592356688"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "ETag", "Value": "W/\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}, {"Name": "label", "Value": "stylesheets/modals.css"}]}, {"Route": "stylesheets/modals.78sdthvano.css", "AssetFile": "stylesheets/modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1818"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}, {"Name": "label", "Value": "stylesheets/modals.css"}]}, {"Route": "stylesheets/modals.78sdthvano.css.gz", "AssetFile": "stylesheets/modals.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "integrity", "Value": "sha256-Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU="}, {"Name": "label", "Value": "stylesheets/modals.css.gz"}]}, {"Route": "stylesheets/modals.css", "AssetFile": "stylesheets/modals.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001592356688"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "ETag", "Value": "W/\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.css", "AssetFile": "stylesheets/modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1818"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.css.gz", "AssetFile": "stylesheets/modals.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU="}]}, {"Route": "stylesheets/typography.css", "AssetFile": "stylesheets/typography.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001631321370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "ETag", "Value": "W/\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.css", "AssetFile": "stylesheets/typography.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1563"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.css.gz", "AssetFile": "stylesheets/typography.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c="}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css", "AssetFile": "stylesheets/typography.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001631321370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "ETag", "Value": "W/\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}, {"Name": "label", "Value": "stylesheets/typography.css"}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css", "AssetFile": "stylesheets/typography.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1563"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}, {"Name": "label", "Value": "stylesheets/typography.css"}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css.gz", "AssetFile": "stylesheets/typography.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "integrity", "Value": "sha256-wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c="}, {"Name": "label", "Value": "stylesheets/typography.css.gz"}]}]}