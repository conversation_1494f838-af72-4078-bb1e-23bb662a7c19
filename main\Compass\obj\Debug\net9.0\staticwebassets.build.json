{"Version": 1, "Hash": "AUJpOIFsZ4TxQFPL2HROBlDluk6HCXQav0MoCTcZ6jY=", "Source": "<PERSON>mp<PERSON>", "BasePath": "_content/Compass", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Compass\\wwwroot", "Source": "<PERSON>mp<PERSON>", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "css/lap-assessment-summary#[.{fingerprint=n8wh2mfms8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rco1797kwl", "Integrity": "dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "FileLength": 1277, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint=xegrsle1tr}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap8fprquuc", "Integrity": "zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "FileLength": 7050, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/buttons#[.{fingerprint=bo27jtqb4m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqsll1jzfq", "Integrity": "RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "FileLength": 949, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/fonts#[.{fingerprint=en88zmxjpn}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1edd6l2s4", "Integrity": "XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "FileLength": 256, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/layout#[.{fingerprint=hni17gahw5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hnnp3rbu3x", "Integrity": "/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "FileLength": 1492, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/colors#[.{fingerprint=si6stszvb5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcq0vr1lul", "Integrity": "pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "FileLength": 592, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/forms#[.{fingerprint=3rfrqp8vgl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r03ioh3bfh", "Integrity": "NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "FileLength": 1071, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint=6gzpyzhau4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "joqzyw7ssu", "Integrity": "LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 23897, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/typography#[.{fingerprint=gj0vo58ahm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7bsxntehs", "Integrity": "wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "FileLength": 612, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint=xegrsle1tr}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ap8fprquuc", "Integrity": "zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "FileLength": 7050, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint=8inm30yfxf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbl5ivwo6k", "Integrity": "Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 74905, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "css/components/non-contact-days#[.{fingerprint=fynul0i6py}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fciai1e7r", "Integrity": "o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "FileLength": 246, "LastWriteTime": "2025-06-05T19:02:10+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/modals#[.{fingerprint=78sdthvano}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6wbrcxc5wq", "Integrity": "Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "FileLength": 627, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Compass", "RelativePath": "app#[.{fingerprint=67x8kjth1g}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqvvn45c3v", "Integrity": "l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "FileLength": 1304, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "xegrsle1tr", "Integrity": "J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "FileLength": 39439, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Compass", "RelativePath": "Compass#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "xegrsle1tr", "Integrity": "J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "FileLength": 39439, "LastWriteTime": "2025-06-16T16:07:13+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "67x8kjth1g", "Integrity": "Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2355, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6gzpyzhau4", "Integrity": "SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 162726, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\c4l-favicon.png", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "c4l-favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dx84q9xhh9", "Integrity": "MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\c4l-favicon.png", "FileLength": 2087, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "css/components/non-contact-days#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fynul0i6py", "Integrity": "OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\components\\non-contact-days.css", "FileLength": 450, "LastWriteTime": "2025-06-05T18:24:55+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "css/lap-assessment-summary#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n8wh2mfms8", "Integrity": "iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\lap-assessment-summary.css", "FileLength": 5729, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next bold (700)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sbn1h6rvg6", "Integrity": "qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next bold (700).woff", "FileLength": 37780, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next bold (700)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ksmoc5bde", "Integrity": "6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next bold (700).woff2", "FileLength": 27080, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next demi (600)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gt4l9a5sa4", "Integrity": "pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next demi (600).woff", "FileLength": 37284, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next demi (600)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nlyaynwm5a", "Integrity": "q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next demi (600).woff2", "FileLength": 26820, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next heavy (900)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0qzgaa16u2", "Integrity": "KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next heavy (900).woff", "FileLength": 37412, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next heavy (900)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "44ancgt2kr", "Integrity": "9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next heavy (900).woff2", "FileLength": 26884, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next medium (500)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dzm9ctkcvu", "Integrity": "Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next medium (500).woff", "FileLength": 37272, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next medium (500)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpd8crsurt", "Integrity": "ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next medium (500).woff2", "FileLength": 26940, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next regular (400)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v16dk9xw7", "Integrity": "EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next regular (400).woff", "FileLength": 36924, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next regular (400)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eg2tnwu7tj", "Integrity": "rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next regular (400).woff2", "FileLength": 26684, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next ultralight (200)#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kn95wx0slf", "Integrity": "IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next ultralight (200).woff", "FileLength": 36732, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff2", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "fonts/Avenir Next ultralight (200)#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1b6w4mpblx", "Integrity": "7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fonts\\Avenir Next ultralight (200).woff2", "FileLength": 26488, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/buttons#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bo27jtqb4m", "Integrity": "S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\buttons.css", "FileLength": 4273, "LastWriteTime": "2025-06-05T18:24:55+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/colors#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "si6stszvb5", "Integrity": "/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\colors.css", "FileLength": 2877, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/fonts#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "en88zmxjpn", "Integrity": "ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\fonts.css", "FileLength": 1534, "LastWriteTime": "2025-03-06T17:07:54+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/forms#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3rfrqp8vgl", "Integrity": "fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\forms.css", "FileLength": 3507, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/layout#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hni17gahw5", "Integrity": "rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\layout.css", "FileLength": 6057, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/modals#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "78sdthvano", "Integrity": "2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\modals.css", "FileLength": 1818, "LastWriteTime": "2025-06-16T14:19:52+00:00"}, {"Identity": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "SourceId": "<PERSON>mp<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\", "BasePath": "_content/Compass", "RelativePath": "stylesheets/typography#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gj0vo58ahm", "Integrity": "hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\stylesheets\\typography.css", "FileLength": 1563, "LastWriteTime": "2025-06-05T18:24:55+00:00"}], "Endpoints": [{"Route": "app.67x8kjth1g.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000766283525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.67x8kjth1g.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2355"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.67x8kjth1g.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67x8kjth1g"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg="}]}, {"Route": "app.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000766283525"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2355"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Eio5BJQyxp9opGDrxkv7DIQSOw4GzvM886RSAIMsEiA="}]}, {"Route": "app.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\uk3d3f7xid-67x8kjth1g.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1304"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l/bLyL3w0WzB7Wj4rkKe+AV1ywbh3ESOBwQA/EaUOTg="}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.6gzpyzhau4.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6gzpyzhau4"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.gz"}, {"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041844506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162726"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SiIVMGgRhdXjKSTIddX7mh9IbOXVcwQWc7/p4nS6D/0="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map.gz"}, {"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "bootstrap/bootstrap.min.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\j1upfsthu6-6gzpyzhau4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23897"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LTxu1aeZz5yJTTAB+DiLnn5XOMdK1oiypNP/4JaeK9c="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000013350065"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\p5qwfbh2yq-8inm30yfxf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74905"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kym5HpgDYV8Caci50HDUPG6WCQ6nTP+8hy429w+zHVU="}]}, {"Route": "c4l-favicon.dx84q9xhh9.png", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\c4l-favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2087"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dx84q9xhh9"}, {"Name": "label", "Value": "c4l-favicon.png"}, {"Name": "integrity", "Value": "sha256-MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II="}]}, {"Route": "c4l-favicon.png", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\c4l-favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2087"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MXRVySiKsEw4dmIvQ+xDj3cf7VF9eGJz8QrpDm1q6II="}]}, {"Route": "Compass.bundle.scp.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.bundle.scp.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.bundle.scp.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}]}, {"Route": "Compass.styles.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.styles.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.styles.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}]}, {"Route": "Compass.xegrsle1tr.bundle.scp.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.xegrsle1tr.bundle.scp.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Compass.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.xegrsle1tr.bundle.scp.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\7f6ta84bp0-xegrsle1tr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}]}, {"Route": "Compass.xegrsle1tr.styles.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000141823855"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.styles.css"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.xegrsle1tr.styles.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Compass.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39439"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.styles.css"}, {"Name": "integrity", "Value": "sha256-J0RVp7TV4KrTm3PiuKebfh7sFdQo4anDYALlzu7Et7Q="}]}, {"Route": "Compass.xegrsle1tr.styles.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\nf1104f9bx-xegrsle1tr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7050"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xegrsle1tr"}, {"Name": "label", "Value": "Compass.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-zv6rIg7EiORGnyLgKi1+MPPdZatxlJPCAYzAsh3AwvQ="}]}, {"Route": "css/components/non-contact-days.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004048582996"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "450"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI="}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.004048582996"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "label", "Value": "css/components/non-contact-days.css"}, {"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\components\\non-contact-days.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "450"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "label", "Value": "css/components/non-contact-days.css"}, {"Name": "integrity", "Value": "sha256-OVZKSUvhIAS/tmC69W+96odLSs4kWOZLwcBM8pFVd8E="}]}, {"Route": "css/components/non-contact-days.fynul0i6py.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\s985n9o26b-fynul0i6py.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fynul0i6py"}, {"Name": "label", "Value": "css/components/non-contact-days.css.gz"}, {"Name": "integrity", "Value": "sha256-o47JF6obcqbC7eckix5qI2HtJMwR30ie+7/CKv926qI="}]}, {"Route": "css/lap-assessment-summary.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000782472613"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA="}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000782472613"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "label", "Value": "css/lap-assessment-summary.css"}, {"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\css\\lap-assessment-summary.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5729"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "label", "Value": "css/lap-assessment-summary.css"}, {"Name": "integrity", "Value": "sha256-iXjulaWikc7bhQ0TyKy8jetEcHlQxDO42T6IFAwjBuo="}]}, {"Route": "css/lap-assessment-summary.n8wh2mfms8.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\1oias294u8-n8wh2mfms8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n8wh2mfms8"}, {"Name": "label", "Value": "css/lap-assessment-summary.css.gz"}, {"Name": "integrity", "Value": "sha256-dTOxU3CDrg2REqRX8WRF1jkgfhjEkE97o43I5DWViRA="}]}, {"Route": "fonts/Avenir Next bold (700).5ksmoc5bde.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27080"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ksmoc5bde"}, {"Name": "label", "Value": "fonts/Avenir Next bold (700).woff2"}, {"Name": "integrity", "Value": "sha256-6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU="}]}, {"Route": "fonts/Avenir Next bold (700).sbn1h6rvg6.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37780"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sbn1h6rvg6"}, {"Name": "label", "Value": "fonts/Avenir Next bold (700).woff"}, {"Name": "integrity", "Value": "sha256-qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q="}]}, {"Route": "fonts/Avenir Next bold (700).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37780"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNqCSou5iFqTC7oD+jj+k29vzMBoHTZ8sSPLhCKJH8Q="}]}, {"Route": "fonts/Avenir Next bold (700).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next bold (700).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27080"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6+173xpT8jvfhG7tqP/K3A8T3ZD69vBwfEroUmPHHZU="}]}, {"Route": "fonts/Avenir Next demi (600).gt4l9a5sa4.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37284"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gt4l9a5sa4"}, {"Name": "label", "Value": "fonts/Avenir Next demi (600).woff"}, {"Name": "integrity", "Value": "sha256-pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk="}]}, {"Route": "fonts/Avenir Next demi (600).nlyaynwm5a.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26820"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nlyaynwm5a"}, {"Name": "label", "Value": "fonts/Avenir Next demi (600).woff2"}, {"Name": "integrity", "Value": "sha256-q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE="}]}, {"Route": "fonts/Avenir Next demi (600).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37284"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pGwEoSx0e9xUwzzkkrpdwFTJvGJcD7oU/EEANPU52Gk="}]}, {"Route": "fonts/Avenir Next demi (600).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next demi (600).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26820"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q2tMZDCdxBF+UHKMe5xa/kdWwAT265NQsd2uznstgkE="}]}, {"Route": "fonts/Avenir Next heavy (900).0qzgaa16u2.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37412"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0qzgaa16u2"}, {"Name": "label", "Value": "fonts/Avenir Next heavy (900).woff"}, {"Name": "integrity", "Value": "sha256-KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ="}]}, {"Route": "fonts/Avenir Next heavy (900).44ancgt2kr.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26884"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "44ancgt2kr"}, {"Name": "label", "Value": "fonts/Avenir Next heavy (900).woff2"}, {"Name": "integrity", "Value": "sha256-9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s="}]}, {"Route": "fonts/Avenir Next heavy (900).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37412"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KKVej9I2/bPtgoOACRNF0IpYWuaDE4hqKy7LVtOCzBQ="}]}, {"Route": "fonts/Avenir Next heavy (900).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next heavy (900).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26884"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9K7vOI9X9R1BoPJdB9cUcugdl683RAmyyBEeOHgrq7s="}]}, {"Route": "fonts/Avenir Next medium (500).bpd8crsurt.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26940"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpd8crsurt"}, {"Name": "label", "Value": "fonts/Avenir Next medium (500).woff2"}, {"Name": "integrity", "Value": "sha256-ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk="}]}, {"Route": "fonts/Avenir Next medium (500).dzm9ctkcvu.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37272"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dzm9ctkcvu"}, {"Name": "label", "Value": "fonts/Avenir Next medium (500).woff"}, {"Name": "integrity", "Value": "sha256-Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM="}]}, {"Route": "fonts/Avenir Next medium (500).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "37272"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z8OBQ8Hxfqi5rk2szivYRUvQxXoaqGAB9YzPgHanuZM="}]}, {"Route": "fonts/Avenir Next medium (500).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next medium (500).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26940"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ofAGcQiN9UnyYRkUxDoqs0vA/0EkAV7Mpy+QAY7U0Wk="}]}, {"Route": "fonts/Avenir Next regular (400).4v16dk9xw7.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36924"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v16dk9xw7"}, {"Name": "label", "Value": "fonts/Avenir Next regular (400).woff"}, {"Name": "integrity", "Value": "sha256-EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY="}]}, {"Route": "fonts/Avenir Next regular (400).eg2tnwu7tj.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26684"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eg2tnwu7tj"}, {"Name": "label", "Value": "fonts/Avenir Next regular (400).woff2"}, {"Name": "integrity", "Value": "sha256-rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ="}]}, {"Route": "fonts/Avenir Next regular (400).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36924"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EOTPNk8m9WT7G4oiMeNToZGzvSRLAZXyk71pJMjRLzY="}]}, {"Route": "fonts/Avenir Next regular (400).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next regular (400).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26684"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rkgXHuNATK1+07CewM5VKDtcg3Y8NXZdm9jTU29E4PQ="}]}, {"Route": "fonts/Avenir Next ultralight (200).1b6w4mpblx.woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26488"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1b6w4mpblx"}, {"Name": "label", "Value": "fonts/Avenir Next ultralight (200).woff2"}, {"Name": "integrity", "Value": "sha256-7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc="}]}, {"Route": "fonts/Avenir Next ultralight (200).kn95wx0slf.woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36732"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kn95wx0slf"}, {"Name": "label", "Value": "fonts/Avenir Next ultralight (200).woff"}, {"Name": "integrity", "Value": "sha256-IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI="}]}, {"Route": "fonts/Avenir Next ultralight (200).woff", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "36732"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IATn++DSDaFYC9mSxLn8RL2SxXYFQxkeLeDyjXX4JOI="}]}, {"Route": "fonts/Avenir Next ultralight (200).woff2", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\fonts\\Avenir Next ultralight (200).woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26488"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7dO1FNABrexOFE2Odvh3QDvX46Wa1seGStpT5meRjtc="}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001052631579"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "label", "Value": "stylesheets/buttons.css"}, {"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4273"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "label", "Value": "stylesheets/buttons.css"}, {"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.bo27jtqb4m.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bo27jtqb4m"}, {"Name": "label", "Value": "stylesheets/buttons.css.gz"}, {"Name": "integrity", "Value": "sha256-RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w="}]}, {"Route": "stylesheets/buttons.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001052631579"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\buttons.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4273"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S/DA38WunCJbmusHTXNmZP3f9cQlLGVfMZk86L7hm9Y="}]}, {"Route": "stylesheets/buttons.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\98ux8qnhmy-bo27jtqb4m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RrITETX3p6CPFhdRf1YwXQmA/FHPMUXQlamQc3EkC5w="}]}, {"Route": "stylesheets/colors.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001686340641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4="}]}, {"Route": "stylesheets/colors.si6stszvb5.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001686340641"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "label", "Value": "stylesheets/colors.css"}, {"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.si6stszvb5.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "label", "Value": "stylesheets/colors.css"}, {"Name": "integrity", "Value": "sha256-/NKCgfE61y+hRLPct9gu+wz3hgrhc7FsQtcYTkHkgfk="}]}, {"Route": "stylesheets/colors.si6stszvb5.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\ed42wihjjg-si6stszvb5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "si6stszvb5"}, {"Name": "label", "Value": "stylesheets/colors.css.gz"}, {"Name": "integrity", "Value": "sha256-pV+5mO5y84KlTCCtXi6fsANwN6O2N9OXZZVA7hYYLC4="}]}, {"Route": "stylesheets/fonts.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1534"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE="}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.003891050584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "label", "Value": "stylesheets/fonts.css"}, {"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\fonts.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1534"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI=\""}, {"Name": "Last-Modified", "Value": "Thu, 06 Mar 2025 17:07:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "label", "Value": "stylesheets/fonts.css"}, {"Name": "integrity", "Value": "sha256-ysj1lwEFe9F+e0QCyozvro0/JT6bMMQyYCuN5eOw1eI="}]}, {"Route": "stylesheets/fonts.en88zmxjpn.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\awlwvxhc7u-en88zmxjpn.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "256"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "en88zmxjpn"}, {"Name": "label", "Value": "stylesheets/fonts.css.gz"}, {"Name": "integrity", "Value": "sha256-XV072/gFUeJ4n0LKx/LLXrBXj6EOQnHgNERd0kAIbFE="}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000932835821"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "label", "Value": "stylesheets/forms.css"}, {"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3507"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "label", "Value": "stylesheets/forms.css"}, {"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.3rfrqp8vgl.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3rfrqp8vgl"}, {"Name": "label", "Value": "stylesheets/forms.css.gz"}, {"Name": "integrity", "Value": "sha256-NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc="}]}, {"Route": "stylesheets/forms.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000932835821"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3507"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fybuyVo5M/VI8+dQhSqlRt32Vcbyl7hfISSb2+/F4R8="}]}, {"Route": "stylesheets/forms.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\gtf55ja8em-3rfrqp8vgl.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NTRaFN9ehimAMoiMZm3QIGfezzYciCZf2wM3UQ0FsMc="}]}, {"Route": "stylesheets/layout.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6057"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI="}]}, {"Route": "stylesheets/layout.hni17gahw5.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "label", "Value": "stylesheets/layout.css"}, {"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.hni17gahw5.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\layout.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6057"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "label", "Value": "stylesheets/layout.css"}, {"Name": "integrity", "Value": "sha256-rZvLpGduXHNRy3mAcNFT5/x4jFWySzuHXxHbkNTjREg="}]}, {"Route": "stylesheets/layout.hni17gahw5.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\e9t64j27iv-hni17gahw5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hni17gahw5"}, {"Name": "label", "Value": "stylesheets/layout.css.gz"}, {"Name": "integrity", "Value": "sha256-/aLWoDrDCGQBOCOYEQ8FAXAIu5vR1dK257cWEgl3GCI="}]}, {"Route": "stylesheets/modals.78sdthvano.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001592356688"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "label", "Value": "stylesheets/modals.css"}, {"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.78sdthvano.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1818"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "label", "Value": "stylesheets/modals.css"}, {"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.78sdthvano.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "78sdthvano"}, {"Name": "label", "Value": "stylesheets/modals.css.gz"}, {"Name": "integrity", "Value": "sha256-Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU="}]}, {"Route": "stylesheets/modals.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001592356688"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\modals.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1818"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 14:19:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Z6xTzdwud+cU0ync7pyZh/GfSeqq2cRL9vM8p+o0dA="}]}, {"Route": "stylesheets/modals.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\tvs8p0hz1j-78sdthvano.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "627"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 16:07:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ctt1SUJxvCe7EsAC7c3UjcNN99uc0vED8yFtgYdHwgU="}]}, {"Route": "stylesheets/typography.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001631321370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1563"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c="}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001631321370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "label", "Value": "stylesheets/typography.css"}, {"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\wwwroot\\stylesheets\\typography.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1563"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 18:24:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "label", "Value": "stylesheets/typography.css"}, {"Name": "integrity", "Value": "sha256-hJ/dfzefQhyLDxxqCLVyhrs3kM9oTKedcBeBMaQ8fwU="}]}, {"Route": "stylesheets/typography.gj0vo58ahm.css.gz", "AssetFile": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\obj\\Debug\\net9.0\\compressed\\m7g0kx1tqw-gj0vo58ahm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 19:02:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gj0vo58ahm"}, {"Name": "label", "Value": "stylesheets/typography.css.gz"}, {"Name": "integrity", "Value": "sha256-wfRTsQuGXEB8tRuLqLkrCxW7yU4MvdYWVeQqoD9op+c="}]}]}