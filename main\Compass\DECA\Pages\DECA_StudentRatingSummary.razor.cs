using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Compass.Common.Services;
using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;

namespace Compass.DECA.Pages
{
    public partial class DECA_StudentRatingSummary : IDisposable
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }

        [Inject]
        public required UserAccessor UserAccessor { get; set; }

        [Inject]
        public required UserSessionService UserSessionService { get; set; }

        [Inject]
        public required CommonSessionDataObserver CommonSessionDataObserver { get; set; }

        [Inject]
        public required IDecaStudentRatingService DecaStudentRatingService { get; set; }

        private bool isLoading = true;
        private List<StudentGroupRatingDto>? studentRatings;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private long? currentOrganizationId;
        private long? currentStudentGroupId;

        protected override async Task OnInitializedAsync()
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                var commonSessionData = await GetCommonSessionData();
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                    currentStudentGroupId = commonSessionData.CurrentStudentGroupId;

                    if (currentStudentGroupId.HasValue)
                    {
                        var ratings = await DecaStudentRatingService.GetCurrentStudentRatingsForStudentGroupAsync(currentStudentGroupId.Value);

                        // Sort by rating date descending
                        studentRatings = ratings
                            .OrderBy(r => r.RatingDate)
                            .ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error or handle appropriately
                Console.WriteLine($"Error loading DECA student ratings: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        public void Dispose()
        {
            // Clean up any resources if needed
        }
    }
}
