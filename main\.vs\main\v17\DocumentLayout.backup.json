{"Version": 1, "WorkspaceRootPath": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Models\\DecaStudentContact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\Models\\DecaStudentContact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\DTOs\\DecaStudentContactDto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Compass\\DECA\\DTOs\\DecaStudentContactDto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DECA_StudentGroupRatingSummary.razor.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs", "RelativeToolTip": "Compass\\DECA\\Pages\\DECA_StudentGroupRatingSummary.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:02:21.102Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DecaStudentContactRepository.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "RelativeToolTip": "Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:24:59.742Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DECA_SummaryContactScreen.razor.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "RelativeToolTip": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:24:41.758Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DecaStudentContact.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Models\\DecaStudentContact.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Models\\DecaStudentContact.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Models\\DecaStudentContact.cs", "RelativeToolTip": "Compass\\DECA\\Models\\DecaStudentContact.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:24:10.063Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IDecaStudentContactRepository.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "RelativeToolTip": "Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:24:02.102Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DecaStudentContactDto.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "RelativeDocumentMoniker": "Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "RelativeToolTip": "Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:23:57.665Z", "EditorCaption": ""}]}]}]}