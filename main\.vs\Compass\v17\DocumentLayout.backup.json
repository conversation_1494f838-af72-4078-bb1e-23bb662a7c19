{"Version": 1, "WorkspaceRootPath": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\services\\decasessiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\services\\decasessiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\repositories\\decastudentcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\repositories\\decastudentcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\models\\decastudentcontact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\models\\decastudentcontact.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\interfaces\\repositories\\idecastudentcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\interfaces\\repositories\\idecastudentcontactrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\dtos\\decastudentcontactdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\dtos\\decastudentcontactdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\pages\\summary\\summary contact\\deca_summarycontactscreen.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\pages\\summary\\summary contact\\deca_summarycontactscreen.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\deca\\pages\\summary\\summary contact\\deca_summarycontactscreen.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\deca\\pages\\summary\\summary contact\\deca_summarycontactscreen.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\common\\helpers\\studentgrouprosterlistaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\common\\helpers\\studentgrouprosterlistaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\common\\pages\\admin\\general\\generaluserslist.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\common\\pages\\admin\\general\\generaluserslist.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\common\\pages\\admin\\general\\generaluserslist.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\common\\pages\\admin\\general\\generaluserslist.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\common\\pages\\admin\\student\\studentsummarycomponent.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\common\\pages\\admin\\student\\studentsummarycomponent.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\common\\pages\\admin\\student\\studentsummarycomponent.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\common\\pages\\admin\\student\\studentsummarycomponent.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|d:\\workspace\\gitrepos\\kelc\\compass-blazor-branches\\main\\compass\\c4l\\services\\c4lsessiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{516E9F7D-07C3-46FD-A4AB-5B1A9A49BE24}|Compass\\Compass.csproj|solutionrelative:compass\\c4l\\services\\c4lsessiondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DECASessionData.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Services\\DECASessionData.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Services\\DECASessionData.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Services\\DECASessionData.cs", "RelativeToolTip": "Compass\\DECA\\Services\\DECASessionData.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:22:39.975Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DECA_SummaryContactScreen.razor", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor", "RelativeDocumentMoniker": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor", "RelativeToolTip": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-16T14:20:58.209Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DECA_SummaryContactScreen.razor.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "RelativeToolTip": "Compass\\DECA\\Pages\\Summary\\Summary Contact\\DECA_SummaryContactScreen.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:21:09.98Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DecaStudentContactDto.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "RelativeDocumentMoniker": "Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "RelativeToolTip": "Compass\\DECA\\DTOs\\DecaStudentContactDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:21:26.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IDecaStudentContactRepository.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "RelativeToolTip": "Compass\\DECA\\Interfaces\\Repositories\\IDecaStudentContactRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:21:39.046Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DecaStudentContactRepository.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "RelativeToolTip": "Compass\\DECA\\Repositories\\DecaStudentContactRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:22:24.721Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DecaStudentContact.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Models\\DecaStudentContact.cs", "RelativeDocumentMoniker": "Compass\\DECA\\Models\\DecaStudentContact.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\DECA\\Models\\DecaStudentContact.cs", "RelativeToolTip": "Compass\\DECA\\Models\\DecaStudentContact.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T14:21:48.375Z", "EditorCaption": ""}]}]}, {"Orientation": 0, "VerticalTabListWidth": 256, "FloatingWindowState": {"Id": "961f55db-6926-4d68-8547-3b630b4ecd02", "Display": 1, "X": -5, "Y": 9, "Width": 1090, "Height": 1030, "WindowState": 0}, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 11, "Title": "StudentSummaryComponent.razor.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor.cs", "RelativeDocumentMoniker": "Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor.cs", "RelativeToolTip": "Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T15:56:22.726Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "StudentSummaryComponent.razor", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor", "RelativeDocumentMoniker": "Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor", "RelativeToolTip": "Compass\\Common\\Pages\\Admin\\Student\\StudentSummaryComponent.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-06T15:56:14.406Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "C4LSessionData.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\C4L\\Services\\C4LSessionData.cs", "RelativeDocumentMoniker": "Compass\\C4L\\Services\\C4LSessionData.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\C4L\\Services\\C4LSessionData.cs", "RelativeToolTip": "Compass\\C4L\\Services\\C4LSessionData.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-05T20:25:55.562Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "GeneralUsersList.razor.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor.cs", "RelativeDocumentMoniker": "Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor.cs", "RelativeToolTip": "Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor.cs", "ViewState": "AgIAADMAAAAAAAAAAAAowE4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T14:36:34.905Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "StudentGroupRosterListAction.cs", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Helpers\\StudentGroupRosterListAction.cs", "RelativeDocumentMoniker": "Compass\\Common\\Helpers\\StudentGroupRosterListAction.cs", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Helpers\\StudentGroupRosterListAction.cs", "RelativeToolTip": "Compass\\Common\\Helpers\\StudentGroupRosterListAction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T18:04:53.696Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "GeneralUsersList.razor", "DocumentMoniker": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor", "RelativeDocumentMoniker": "Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor", "ToolTip": "D:\\Workspace\\GitRepos\\KELC\\Compass-Blazor-branches\\main\\Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor", "RelativeToolTip": "Compass\\Common\\Pages\\Admin\\General\\GeneralUsersList.razor", "ViewState": "AgIAAAMAAAAAAAAAAAAAABgAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-06T14:36:06.012Z"}]}]}]}